﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{9C13DECF-125D-3D30-86F1-D869EAAE20E3}"
	ProjectSection(ProjectDependencies) = postProject
		{6CFF06D8-6E53-31E1-8022-94FA82C5A623} = {6CFF06D8-6E53-31E1-8022-94FA82C5A623}
		{F66EBFA4-B73E-3F94-B4E4-58480FD4885C} = {F66EBFA4-B73E-3F94-B4E4-58480FD4885C}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{CBF1FC12-ACCC-3904-B402-063740D6A2C8}"
	ProjectSection(ProjectDependencies) = postProject
		{9C13DECF-125D-3D30-86F1-D869EAAE20E3} = {9C13DECF-125D-3D30-86F1-D869EAAE20E3}
		{6CFF06D8-6E53-31E1-8022-94FA82C5A623} = {6CFF06D8-6E53-31E1-8022-94FA82C5A623}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "..\..\\ZERO_CHECK.vcxproj", "{6CFF06D8-6E53-31E1-8022-94FA82C5A623}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "connectivity_plus_plugin", "connectivity_plus_plugin.vcxproj", "{F66EBFA4-B73E-3F94-B4E4-58480FD4885C}"
	ProjectSection(ProjectDependencies) = postProject
		{6CFF06D8-6E53-31E1-8022-94FA82C5A623} = {6CFF06D8-6E53-31E1-8022-94FA82C5A623}
		{326F7609-8E43-3528-92A7-2327E8EFDA25} = {326F7609-8E43-3528-92A7-2327E8EFDA25}
		{78D17EA6-452B-37FA-9AFE-D3A66F94C799} = {78D17EA6-452B-37FA-9AFE-D3A66F94C799}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_assemble", "..\..\flutter\flutter_assemble.vcxproj", "{326F7609-8E43-3528-92A7-2327E8EFDA25}"
	ProjectSection(ProjectDependencies) = postProject
		{6CFF06D8-6E53-31E1-8022-94FA82C5A623} = {6CFF06D8-6E53-31E1-8022-94FA82C5A623}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_wrapper_plugin", "..\..\flutter\flutter_wrapper_plugin.vcxproj", "{78D17EA6-452B-37FA-9AFE-D3A66F94C799}"
	ProjectSection(ProjectDependencies) = postProject
		{6CFF06D8-6E53-31E1-8022-94FA82C5A623} = {6CFF06D8-6E53-31E1-8022-94FA82C5A623}
		{326F7609-8E43-3528-92A7-2327E8EFDA25} = {326F7609-8E43-3528-92A7-2327E8EFDA25}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Profile|x64 = Profile|x64
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{9C13DECF-125D-3D30-86F1-D869EAAE20E3}.Debug|x64.ActiveCfg = Debug|x64
		{9C13DECF-125D-3D30-86F1-D869EAAE20E3}.Debug|x64.Build.0 = Debug|x64
		{9C13DECF-125D-3D30-86F1-D869EAAE20E3}.Profile|x64.ActiveCfg = Profile|x64
		{9C13DECF-125D-3D30-86F1-D869EAAE20E3}.Profile|x64.Build.0 = Profile|x64
		{9C13DECF-125D-3D30-86F1-D869EAAE20E3}.Release|x64.ActiveCfg = Release|x64
		{9C13DECF-125D-3D30-86F1-D869EAAE20E3}.Release|x64.Build.0 = Release|x64
		{CBF1FC12-ACCC-3904-B402-063740D6A2C8}.Debug|x64.ActiveCfg = Debug|x64
		{CBF1FC12-ACCC-3904-B402-063740D6A2C8}.Profile|x64.ActiveCfg = Profile|x64
		{CBF1FC12-ACCC-3904-B402-063740D6A2C8}.Release|x64.ActiveCfg = Release|x64
		{6CFF06D8-6E53-31E1-8022-94FA82C5A623}.Debug|x64.ActiveCfg = Debug|x64
		{6CFF06D8-6E53-31E1-8022-94FA82C5A623}.Debug|x64.Build.0 = Debug|x64
		{6CFF06D8-6E53-31E1-8022-94FA82C5A623}.Profile|x64.ActiveCfg = Profile|x64
		{6CFF06D8-6E53-31E1-8022-94FA82C5A623}.Profile|x64.Build.0 = Profile|x64
		{6CFF06D8-6E53-31E1-8022-94FA82C5A623}.Release|x64.ActiveCfg = Release|x64
		{6CFF06D8-6E53-31E1-8022-94FA82C5A623}.Release|x64.Build.0 = Release|x64
		{F66EBFA4-B73E-3F94-B4E4-58480FD4885C}.Debug|x64.ActiveCfg = Debug|x64
		{F66EBFA4-B73E-3F94-B4E4-58480FD4885C}.Debug|x64.Build.0 = Debug|x64
		{F66EBFA4-B73E-3F94-B4E4-58480FD4885C}.Profile|x64.ActiveCfg = Profile|x64
		{F66EBFA4-B73E-3F94-B4E4-58480FD4885C}.Profile|x64.Build.0 = Profile|x64
		{F66EBFA4-B73E-3F94-B4E4-58480FD4885C}.Release|x64.ActiveCfg = Release|x64
		{F66EBFA4-B73E-3F94-B4E4-58480FD4885C}.Release|x64.Build.0 = Release|x64
		{326F7609-8E43-3528-92A7-2327E8EFDA25}.Debug|x64.ActiveCfg = Debug|x64
		{326F7609-8E43-3528-92A7-2327E8EFDA25}.Debug|x64.Build.0 = Debug|x64
		{326F7609-8E43-3528-92A7-2327E8EFDA25}.Profile|x64.ActiveCfg = Profile|x64
		{326F7609-8E43-3528-92A7-2327E8EFDA25}.Profile|x64.Build.0 = Profile|x64
		{326F7609-8E43-3528-92A7-2327E8EFDA25}.Release|x64.ActiveCfg = Release|x64
		{326F7609-8E43-3528-92A7-2327E8EFDA25}.Release|x64.Build.0 = Release|x64
		{78D17EA6-452B-37FA-9AFE-D3A66F94C799}.Debug|x64.ActiveCfg = Debug|x64
		{78D17EA6-452B-37FA-9AFE-D3A66F94C799}.Debug|x64.Build.0 = Debug|x64
		{78D17EA6-452B-37FA-9AFE-D3A66F94C799}.Profile|x64.ActiveCfg = Profile|x64
		{78D17EA6-452B-37FA-9AFE-D3A66F94C799}.Profile|x64.Build.0 = Profile|x64
		{78D17EA6-452B-37FA-9AFE-D3A66F94C799}.Release|x64.ActiveCfg = Release|x64
		{78D17EA6-452B-37FA-9AFE-D3A66F94C799}.Release|x64.Build.0 = Release|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {3FC6D365-DFF9-3A52-A4C6-AF6E8ABA417E}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
