import 'package:flutter/material.dart';
import '../models/menu_item.dart';
import '../models/daily_deal.dart';
import '../services/recommendation_service.dart';
import '../services/cart_service.dart';
import '../widgets/deal_card.dart';
import '../screens/menu_item_screen.dart';
import '../screens/hot_deals_page.dart';

class HotDealsSection extends StatefulWidget {
  final CartService cartService;

  const HotDealsSection({Key? key, required this.cartService}) : super(key: key);

  @override
  State<HotDealsSection> createState() => _HotDealsSectionState();
}

class _HotDealsSectionState extends State<HotDealsSection> {
  final RecommendationService _recommendationService = RecommendationService();
  List<MenuItem> _deals = [];
  bool _isLoading = true;
  String? _errorMessage;

  // Daily deals data
  final List<DailyDeal> _dailyDeals = [
    const DailyDeal(
      id: 'tuesday_wings',
      name: 'Wing It Tuesday',
      description: 'Buy 10 Wings, Get 5 Free!',
      price: 14.99,
      category: 'Wings',
      imageUrl: 'assets/images/menu/wings_special.jpg',
      dayOfWeek: 'Tuesday',
      badgeText: 'TUESDAY SPECIAL',
    ),
    const DailyDeal(
      id: 'wednesday_family',
      name: 'Family Wednesday',
      description: '20% off Family Combos',
      price: 29.99,
      category: 'Family Combos',
      imageUrl: 'assets/images/menu/family_combo.jpg',
      dayOfWeek: 'Wednesday',
      badgeText: 'WEDNESDAY DEAL',
    ),
    const DailyDeal(
      id: 'thursday_drinks',
      name: 'Thirsty Thursday',
      description: 'Buy 1 Get 1 Free on all drinks',
      price: 4.99,
      category: 'Drinks',
      imageUrl: 'assets/images/menu/thirsty_thursday.jpg',
      dayOfWeek: 'Thursday',
      badgeText: 'THURSDAY SPECIAL',
    ),
    const DailyDeal(
      id: 'friday_combo',
      name: 'TGIF Special',
      description: 'Free drink with any combo',
      price: 15.99,
      category: 'Combos',
      imageUrl: 'assets/images/menu/tgif_special.jpg',
      dayOfWeek: 'Friday',
      badgeText: 'FRIDAY OFFER',
    ),
    const DailyDeal(
      id: 'sunday_funday',
      name: 'Sunday Funday',
      description: '15% off all orders',
      price: 0.00,
      category: 'All Menu',
      imageUrl: 'assets/images/menu/sunday_special.jpg',
      dayOfWeek: 'Sunday',
      badgeText: 'SUNDAY SPECIAL',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _loadDeals();
  }

  Future<void> _loadDeals() async {
    try {
      final deals = await _recommendationService.getHotDeals();
      if (mounted) {
        setState(() {
          _deals = deals;
          _isLoading = false;
          _errorMessage = null;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Unable to load deals. Please try again later.';
        });
      }
    }
  }

  int _getDayNumber(String day) {
    switch (day.toLowerCase()) {
      case 'monday':
        return 1;
      case 'tuesday':
        return 2;
      case 'wednesday':
        return 3;
      case 'thursday':
        return 4;
      case 'friday':
        return 5;
      case 'saturday':
        return 6;
      case 'sunday':
        return 7;
      default:
        return 0;
    }
  }

  Widget _buildTodaysSpecialCard() {
    final currentDay = DateTime.now().weekday;
    final todayDeal = _dailyDeals.firstWhere(
      (deal) => _getDayNumber(deal.dayOfWeek) == currentDay,
      orElse: () => _dailyDeals[0],
    );
    return DealCard(
      deal: MenuItem(
        id: todayDeal.id,
        name: todayDeal.name,
        description: '${todayDeal.dayOfWeek} Special: ${todayDeal.description}',
        price: todayDeal.price,
        category: todayDeal.category,
        imageUrl: todayDeal.imageUrl,
        isSpecial: true,
        available: true,
      ),
      onTap: () {
        if (todayDeal.category.isNotEmpty) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => MenuItemScreen(
                category: todayDeal.category,
                cartService: widget.cartService,
              ),
            ),
          );
        }
      },
      index: 0,
    );
  }


  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title Section with Gradient
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          padding: const EdgeInsets.fromLTRB(24, 32, 24, 24),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.red[50]?.withValues(alpha: 0.3) ?? Colors.red.withValues(alpha: 0.1),
                Colors.orange[50]?.withValues(alpha: 0.2) ?? Colors.orange.withValues(alpha: 0.05),
              ],
            ),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: Colors.red.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Semantics(
                    label: 'Hot Deals Icon',
                    child: Icon(
                      Icons.local_fire_department,
                      color: Colors.red[700],
                      size: 28,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'HOT DEALS & SPECIALS',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.w900,
                            fontFamily: 'MontserratBlack',
                            color: Colors.red[700],
                          ),
                    ),
                  ),
                ],
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 8),
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            HotDealsPage(
                              dailyDeals: _dailyDeals,
                              regularDeals: _deals,
                              cartService: widget.cartService,
                            ),
                      ),
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red[700],
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 10),
                    textStyle: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  child: const Text('View All Offers'),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        if (_isLoading)
          const Center(
            child: Padding(
              padding: EdgeInsets.all(32.0),
              child: CircularProgressIndicator(),
            ),
          )
        else if (_errorMessage != null)
          Center(
            child: Padding(
              padding: const EdgeInsets.all(32.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    _errorMessage!,
                    style: TextStyle(color: Colors.red[700]),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _loadDeals,
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
          )
        else
          // Combined Deals and Today's Special in horizontal scroll
          SizedBox(
            height: 420, // Fixed height for the deal cards
            child: MouseRegion(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                scrollDirection: Axis.horizontal,
                itemCount: _deals.length + 1, // +1 for today's special
                itemBuilder: (context, index) {
                  if (index == 0) {
                    // Today's Special as the first item
                    return SizedBox(
                      width: 300, // Fixed width for each card
                      child: Padding(
                        padding: const EdgeInsets.only(right: 16),
                        child: _buildTodaysSpecialCard(),
                      ),
                    );
                  } else {
                    // Regular deals
                    final dealIndex = index - 1;
                    return SizedBox(
                      width: 300, // Fixed width for each card
                      child: Padding(
                        padding: const EdgeInsets.only(right: 16),
                        child: DealCard(
                          deal: _deals[dealIndex],
                          onTap: () {
                            if (_deals[dealIndex].category.isNotEmpty) {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => MenuItemScreen(
                                    category: _deals[dealIndex].category,
                                    cartService: widget.cartService,
                                  ),
                                ),
                              );
                            }
                          },
                          index: dealIndex,
                        ),
                      ),
                    );
                  }
                },
              ),
            ),
          ),
      ],
    );
  }
}
