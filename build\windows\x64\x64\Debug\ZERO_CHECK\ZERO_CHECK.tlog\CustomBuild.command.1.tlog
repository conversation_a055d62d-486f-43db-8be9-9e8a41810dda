^D:\BIZNESS\7THSENSEMEDIALABZ\CHICAS CHICKEN FLUTTER\BUILD\WINDOWS\X64\CMAKEFILES\33208E08B88B6FB580AD03E2CDF8D88B\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" "-SD:/Bizness/7thSenseMediaLabz/Chicas Chicken Flutter/windows" "-BD:/Bizness/7thSenseMediaLabz/Chicas Chicken Flutter/build/windows/x64" --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file "D:/Bizness/7thSenseMediaLabz/Chicas Chicken Flutter/build/windows/x64/qsr_app.sln"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
