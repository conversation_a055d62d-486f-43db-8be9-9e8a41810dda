import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import '../lib/screens/terms_of_use_screen.dart';
import '../lib/screens/privacy_policy_screen.dart';

void main() {
  group('Legal Pages Tests', () {
    testWidgets('Terms of Use screen displays correctly', (WidgetTester tester) async {
      // Build the Terms of Use screen
      await tester.pumpWidget(
        const MaterialApp(
          home: TermsOfUseScreen(),
        ),
      );

      // Verify that the screen loads
      expect(find.text('Terms of Use'), findsWidgets);
      expect(find.text('CHICA\'S CHICKEN'), findsOneWidget);
      expect(find.text('1. ACCEPTANCE OF TERMS'), findsOneWidget);
      expect(find.text('2. DESCRIPTION OF SERVICE'), findsOneWidget);
      
      // Verify scrollable content
      expect(find.byType(SingleChildScrollView), findsOneWidget);
    });

    testWidgets('Privacy Policy screen displays correctly', (WidgetTester tester) async {
      // Build the Privacy Policy screen
      await tester.pumpWidget(
        const MaterialApp(
          home: PrivacyPolicyScreen(),
        ),
      );

      // Verify that the screen loads
      expect(find.text('Privacy Policy'), findsWidgets);
      expect(find.text('CHICA\'S CHICKEN'), findsOneWidget);
      expect(find.text('1. INTRODUCTION'), findsOneWidget);
      expect(find.text('2. INFORMATION WE COLLECT'), findsOneWidget);
      
      // Verify scrollable content
      expect(find.byType(SingleChildScrollView), findsOneWidget);
    });

    testWidgets('Terms of Use screen has proper styling', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: TermsOfUseScreen(),
        ),
      );

      // Verify AppBar styling
      final AppBar appBar = tester.widget(find.byType(AppBar));
      expect(appBar.backgroundColor, const Color(0xFFFF5C22));
      expect(appBar.foregroundColor, Colors.white);
    });

    testWidgets('Privacy Policy screen has proper styling', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: PrivacyPolicyScreen(),
        ),
      );

      // Verify AppBar styling
      final AppBar appBar = tester.widget(find.byType(AppBar));
      expect(appBar.backgroundColor, const Color(0xFFFF5C22));
      expect(appBar.foregroundColor, Colors.white);
    });
  });
}
