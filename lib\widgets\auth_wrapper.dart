import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/mock_auth_service.dart';
import '../models/auth_state.dart';
import '../screens/login_screen.dart';
import '../layouts/main_layout.dart';
import '../screens/loading_screen.dart';

/// 🔐 Authentication Wrapper
/// Routes users to appropriate screens based on authentication state
class Auth<PERSON>rapper extends StatefulWidget {
  const AuthWrapper({Key? key}) : super(key: key);

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeAuth();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    
    // Handle app lifecycle changes for security
    final authService = Provider.of<MockAuthService>(context, listen: false);
    
    switch (state) {
      case AppLifecycleState.paused:
        // App is in background, start security timer
        _handleAppPaused();
        break;
      case AppLifecycleState.resumed:
        // App is back in foreground, check security
        _handleAppResumed();
        break;
      case AppLifecycleState.detached:
        // App is being terminated
        _handleAppDetached();
        break;
      default:
        break;
    }
  }

  Future<void> _initializeAuth() async {
    final authService = Provider.of<MockAuthService>(context, listen: false);
    await authService.initialize();
  }

  void _handleAppPaused() {
    // Record when app was paused for security timeout
    final authService = Provider.of<MockAuthService>(context, listen: false);
    // Implementation would save timestamp to secure storage
  }

  void _handleAppResumed() {
    // Check if app was paused too long and require re-authentication
    final authService = Provider.of<MockAuthService>(context, listen: false);
    // Implementation would check timestamp and potentially require biometric auth
  }

  void _handleAppDetached() {
    // Clear sensitive data when app is terminated
    final authService = Provider.of<MockAuthService>(context, listen: false);
    // Implementation would clear temporary sensitive data
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<MockAuthService>(
      builder: (context, authService, child) {
        final authState = authService.authState;
        
        switch (authState.status) {
          case AuthStatus.initial:
          case AuthStatus.loading:
            return const LoadingScreen();
            
          case AuthStatus.authenticated:
            if (!authState.emailVerified) {
              return _buildEmailVerificationScreen(authService);
            }
            return const MainLayout();
            
          case AuthStatus.unauthenticated:
          case AuthStatus.error:
            return const LoginScreen();
        }
      },
    );
  }

  Widget _buildEmailVerificationScreen(MockAuthService authService) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Icon
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.mark_email_unread,
                  size: 60,
                  color: Colors.orange,
                ),
              ),
              
              const SizedBox(height: 32),
              
              // Title
              Text(
                'Verify Your Email',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 16),
              
              // Message
              Text(
                'We\'ve sent a verification email to:',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 8),
              
              // Email
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Text(
                  authService.currentUser?.email ?? '',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFFFF5C22),
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Instructions
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.blue[200]!),
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info_outline, color: Colors.blue[700], size: 20),
                        const SizedBox(width: 8),
                        Text(
                          'Please check your email',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Colors.blue[700],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Click the verification link in your email to activate your account. '
                      'Don\'t forget to check your spam folder!',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.blue[700],
                      ),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 32),
              
              // Resend Email Button
              OutlinedButton(
                onPressed: () async {
                  try {
                    // For mock service, just show success message
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Verification email sent!'),
                        backgroundColor: Colors.green,
                      ),
                    );
                  } catch (e) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Failed to send verification email'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                },
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  side: const BorderSide(color: Color(0xFFFF5C22)),
                  foregroundColor: const Color(0xFFFF5C22),
                ),
                child: const Text(
                  'RESEND EMAIL',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    letterSpacing: 1,
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Refresh Button
              ElevatedButton(
                onPressed: () async {
                  // For mock service, just simulate verification
                  // In a real app, this would reload the user and check verification status
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Email verification status updated!'),
                      backgroundColor: Colors.green,
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFFF5C22),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'I\'VE VERIFIED MY EMAIL',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    letterSpacing: 1,
                  ),
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Sign Out Button
              TextButton(
                onPressed: () async {
                  await authService.signOut();
                },
                child: Text(
                  'Sign out and use different email',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                    decoration: TextDecoration.underline,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// 🔒 Secure Route Guard
/// Protects routes that require authentication
class SecureRoute extends StatelessWidget {
  final Widget child;
  final bool requireEmailVerification;

  const SecureRoute({
    Key? key,
    required this.child,
    this.requireEmailVerification = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<MockAuthService>(
      builder: (context, authService, _) {
        final authState = authService.authState;
        
        // Check if user is authenticated
        if (!authState.isAuthenticated) {
          return const LoginScreen();
        }
        
        // Check email verification if required
        if (requireEmailVerification && !authState.emailVerified) {
          return const AuthWrapper(); // Will show email verification screen
        }
        
        return child;
      },
    );
  }
}

/// 🚫 Guest Route Guard
/// Redirects authenticated users away from auth screens
class GuestRoute extends StatelessWidget {
  final Widget child;

  const GuestRoute({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<MockAuthService>(
      builder: (context, authService, _) {
        final authState = authService.authState;
        
        // If user is authenticated, redirect to main app
        if (authState.isAuthenticated) {
          return const MainLayout();
        }
        
        return child;
      },
    );
  }
}

/// 🔐 Biometric Gate
/// Requires biometric authentication for sensitive operations
class BiometricGate extends StatefulWidget {
  final Widget child;
  final String reason;
  final VoidCallback? onAuthenticationFailed;

  const BiometricGate({
    Key? key,
    required this.child,
    required this.reason,
    this.onAuthenticationFailed,
  }) : super(key: key);

  @override
  State<BiometricGate> createState() => _BiometricGateState();
}

class _BiometricGateState extends State<BiometricGate> {
  bool _isAuthenticated = false;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _authenticateWithBiometrics();
  }

  Future<void> _authenticateWithBiometrics() async {
    final authService = Provider.of<MockAuthService>(context, listen: false);
    
    try {
      final result = await authService.authenticateWithBiometrics();
      
      setState(() {
        _isAuthenticated = result.success;
        _isLoading = false;
      });
      
      if (!result.success) {
        widget.onAuthenticationFailed?.call();
      }
    } catch (e) {
      setState(() {
        _isAuthenticated = false;
        _isLoading = false;
      });
      widget.onAuthenticationFailed?.call();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }
    
    if (!_isAuthenticated) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.fingerprint,
                size: 80,
                color: Colors.grey,
              ),
              const SizedBox(height: 24),
              Text(
                'Biometric Authentication Required',
                style: Theme.of(context).textTheme.headlineSmall,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                widget.reason,
                style: Theme.of(context).textTheme.bodyLarge,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              ElevatedButton(
                onPressed: _authenticateWithBiometrics,
                child: const Text('Try Again'),
              ),
            ],
          ),
        ),
      );
    }
    
    return widget.child;
  }
}
