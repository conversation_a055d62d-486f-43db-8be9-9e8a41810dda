import 'package:flutter/material.dart';
import '../models/menu_item.dart';
import '../services/recommendation_service.dart';
import '../services/cart_service.dart';
import '../widgets/deal_card.dart';
import '../screens/menu_item_screen.dart';

class PersonalizedRecommendationsSection extends StatefulWidget {
  final CartService cartService;

  const PersonalizedRecommendationsSection({Key? key, required this.cartService}) : super(key: key);

  @override
  State<PersonalizedRecommendationsSection> createState() =>
      _PersonalizedRecommendationsSectionState();
}

class _PersonalizedRecommendationsSectionState
    extends State<PersonalizedRecommendationsSection> {
  final RecommendationService _recommendationService = RecommendationService();
  List<MenuItem> _recommendations = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadRecommendations();
  }

  Future<void> _loadRecommendations() async {
    try {
      final recommendations =
          await _recommendationService.getPersonalizedRecommendations();
      setState(() {
        _recommendations = recommendations;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      // Handle error
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title Section with Gradient
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          padding: const EdgeInsets.fromLTRB(24, 32, 24, 24),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.amber[50]?.withValues(alpha: 0.3) ?? Colors.amber.withValues(alpha: 0.1),
                Colors.orange[50]?.withValues(alpha: 0.2) ?? Colors.orange.withValues(alpha: 0.05),
              ],
            ),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: Colors.amber.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'JUST FOR YOU',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w900,
                      fontFamily: 'MontserratBlack',
                      color: Colors.brown[700],
                    ),
              ),
              const SizedBox(height: 8),
              Text(
                'Based on your previous orders',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
        if (_isLoading)
          const Center(
            child: Padding(
              padding: EdgeInsets.all(32.0),
              child: CircularProgressIndicator(),
            ),
          )
        else if (_recommendations.isEmpty)
          Center(
            child: Padding(
              padding: const EdgeInsets.all(32.0),
              child: Text(
                'Check back later for personalized recommendations!',
                style: TextStyle(color: Colors.grey[600]),
              ),
            ),
          )
        else
          SizedBox(
            height: 420, // Fixed height for the recommendation cards
            child: MouseRegion(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                scrollDirection: Axis.horizontal,
                itemCount: _recommendations.length,
                itemBuilder: (context, index) {
                  final recommendation = _recommendations[index];
                  return SizedBox(
                    width: 300, // Fixed width for each card
                    child: Padding(
                      padding: const EdgeInsets.only(right: 16),
                      child: DealCard(
                        deal: _recommendations[index],
                        onTap: () {
                          if (recommendation.category.isNotEmpty) {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => MenuItemScreen(
                                  category: recommendation.category,
                                  cartService: widget.cartService,
                                ),
                              ),
                            );
                          }
                        },
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
      ],
    );
  }
}
