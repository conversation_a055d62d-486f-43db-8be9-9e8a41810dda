import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../models/user_model.dart';
import '../models/auth_state.dart';

/// 🔐 Mock Authentication Service for Development and Web Compatibility
/// Provides full authentication functionality without Firebase dependencies
class MockAuthService extends ChangeNotifier {
  static final MockAuthService _instance = MockAuthService._internal();
  factory MockAuthService() => _instance;
  MockAuthService._internal();

  // Secure storage for sensitive data
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  // State management
  AuthState _authState = AuthState.initial();
  UserModel? _currentUser;
  Timer? _sessionTimer;

  // Configuration
  static const Duration _sessionTimeout = Duration(minutes: 30);
  static const int _maxLoginAttempts = 5;
  static const Duration _lockoutDuration = Duration(minutes: 15);

  // Mock user database (in production, this would be a real backend)
  final Map<String, Map<String, dynamic>> _mockUsers = {};

  // Getters
  AuthState get authState => _authState;
  UserModel? get currentUser => _currentUser;
  bool get isAuthenticated => _authState.isAuthenticated;

  /// Initialize the authentication service
  Future<void> initialize() async {
    try {
      _setAuthState(AuthState.loading());
      
      // Check for existing session
      await _checkExistingSession();
      
    } catch (e) {
      _setAuthState(AuthState.error(message: 'Failed to initialize authentication: $e'));
    }
  }

  /// Check for existing session
  Future<void> _checkExistingSession() async {
    try {
      final lastActivity = await _secureStorage.read(key: 'last_activity');
      final currentUserId = await _secureStorage.read(key: 'current_user_id');
      
      if (lastActivity != null && currentUserId != null) {
        final lastActivityTime = DateTime.parse(lastActivity);
        final now = DateTime.now();
        
        if (now.difference(lastActivityTime) > _sessionTimeout) {
          // Session expired, sign out
          await signOut();
          return;
        }
        
        // Load user from storage
        final userJson = await _secureStorage.read(key: 'user_$currentUserId');
        if (userJson != null) {
          final userData = jsonDecode(userJson);
          _currentUser = UserModel.fromJson(userData);
          _setAuthState(AuthState.authenticated(
            userId: _currentUser!.uid,
            email: _currentUser!.email,
            displayName: _currentUser!.name,
            emailVerified: _currentUser!.emailVerified,
          ));
          
          // Start session timer
          _startSessionTimer();
          return;
        }
      }
      
      _setAuthState(AuthState.unauthenticated());
    } catch (e) {
      _setAuthState(AuthState.error(message: 'Failed to check session: $e'));
    }
  }

  /// Sign up with email and password
  Future<AuthState> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String name,
    String? phone,
  }) async {
    try {
      _setAuthState(AuthState.loading());
      
      // Simulate network delay
      await Future.delayed(const Duration(milliseconds: 1500));
      
      // Check if email is already in use
      if (_mockUsers.containsKey(email)) {
        final error = AuthState.error(
          message: 'An account with this email already exists',
          code: AuthErrorCodes.emailAlreadyInUse,
        );
        _setAuthState(error);
        return error;
      }

      // Create user account
      final userId = 'user_${DateTime.now().millisecondsSinceEpoch}';
      final userProfile = UserModel(
        uid: userId,
        email: email,
        name: name,
        phone: phone,
        emailVerified: true, // Auto-verify for demo
        addresses: [],
        preferences: const UserPreferences(
          favoriteItems: [],
          dietaryRestrictions: [],
          pushNotifications: true,
          emailNotifications: true,
          smsNotifications: false,
          preferredLanguage: 'en',
          biometricAuth: false,
          theme: 'system',
        ),
        loyaltyInfo: const LoyaltyInfo(
          points: 100, // Welcome bonus
          tier: 'Bronze',
          totalSpent: 0,
          totalOrders: 0,
          availableRewards: [],
        ),
        createdAt: DateTime.now(),
        lastLogin: DateTime.now(),
      );

      // Store user in mock database
      _mockUsers[email] = {
        'password': password, // In production, this would be hashed
        'user': userProfile.toJson(),
      };

      // Store user securely
      await _secureStorage.write(
        key: 'user_$userId',
        value: jsonEncode(userProfile.toJson()),
      );

      _currentUser = userProfile;
      _setAuthState(AuthState.authenticated(
        userId: userId,
        email: email,
        displayName: name,
        emailVerified: true,
      ));

      // Update last login and start session
      await _updateLastLogin();
      _startSessionTimer();
      
      return _authState;
    } catch (e) {
      final error = AuthState.error(message: 'Sign up failed: $e');
      _setAuthState(error);
      return error;
    }
  }

  /// Sign in with email and password
  Future<AuthState> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      _setAuthState(AuthState.loading());
      
      // Check login attempts
      if (await _isAccountLocked(email)) {
        final error = AuthState.error(
          message: 'Account temporarily locked due to too many failed attempts',
          code: AuthErrorCodes.tooManyRequests,
        );
        _setAuthState(error);
        return error;
      }

      // Simulate network delay
      await Future.delayed(const Duration(milliseconds: 1000));

      // Check credentials
      if (!_mockUsers.containsKey(email)) {
        await _trackFailedLoginAttempt(email);
        final error = AuthState.error(
          message: 'No account found with this email address',
          code: AuthErrorCodes.userNotFound,
        );
        _setAuthState(error);
        return error;
      }

      final userData = _mockUsers[email]!;
      if (userData['password'] != password) {
        await _trackFailedLoginAttempt(email);
        final error = AuthState.error(
          message: 'Incorrect password',
          code: AuthErrorCodes.wrongPassword,
        );
        _setAuthState(error);
        return error;
      }

      // Clear failed login attempts on success
      await _clearFailedLoginAttempts(email);

      // Load user profile
      _currentUser = UserModel.fromJson(userData['user']);
      _setAuthState(AuthState.authenticated(
        userId: _currentUser!.uid,
        email: _currentUser!.email,
        displayName: _currentUser!.name,
        emailVerified: _currentUser!.emailVerified,
      ));

      // Update last login and start session
      await _updateLastLogin();
      _startSessionTimer();
      
      return _authState;
    } catch (e) {
      final error = AuthState.error(message: 'Sign in failed: $e');
      _setAuthState(error);
      return error;
    }
  }

  /// Sign in with Google (mock implementation)
  Future<AuthState> signInWithGoogle() async {
    try {
      _setAuthState(AuthState.loading());
      
      // Simulate Google sign-in flow
      await Future.delayed(const Duration(milliseconds: 2000));
      
      // Create mock Google user
      final email = '<EMAIL>';
      final name = 'Demo User';
      
      if (!_mockUsers.containsKey(email)) {
        // Create new user
        return await signUpWithEmailAndPassword(
          email: email,
          password: 'demo_password',
          name: name,
        );
      } else {
        // Sign in existing user
        return await signInWithEmailAndPassword(
          email: email,
          password: _mockUsers[email]!['password'],
        );
      }
    } catch (e) {
      final error = AuthState.error(message: 'Google sign in failed: $e');
      _setAuthState(error);
      return error;
    }
  }

  /// Sign out
  Future<void> signOut() async {
    try {
      await _secureStorage.deleteAll();
      _currentUser = null;
      _setAuthState(AuthState.unauthenticated());
      _stopSessionTimer();
    } catch (e) {
      debugPrint('Sign out error: $e');
    }
  }

  /// Send password reset email (mock implementation)
  Future<bool> sendPasswordResetEmail(String email) async {
    try {
      // Simulate email sending delay
      await Future.delayed(const Duration(milliseconds: 1500));
      
      // Check if email exists
      if (!_mockUsers.containsKey(email)) {
        return false;
      }
      
      // In a real implementation, this would send an actual email
      debugPrint('Password reset email sent to: $email');
      return true;
    } catch (e) {
      debugPrint('Password reset error: $e');
      return false;
    }
  }

  /// Check if biometric authentication is available (mock)
  Future<bool> isBiometricAvailable() async {
    // For web, return false. For mobile, this would check actual biometric availability
    return !kIsWeb;
  }

  /// Authenticate with biometrics (mock)
  Future<BiometricAuthResult> authenticateWithBiometrics() async {
    try {
      if (kIsWeb) {
        return BiometricAuthResult.failure(
          message: 'Biometric authentication not available on web',
          errorType: BiometricAuthError.notAvailable,
        );
      }
      
      // Simulate biometric authentication
      await Future.delayed(const Duration(milliseconds: 1000));
      
      // For demo, always succeed
      return BiometricAuthResult.success();
    } catch (e) {
      return BiometricAuthResult.failure(
        message: 'Biometric authentication failed: $e',
        errorType: BiometricAuthError.unknown,
      );
    }
  }

  /// Update user profile
  Future<bool> updateUserProfile(UserModel updatedUser) async {
    try {
      // Update in mock database
      if (_mockUsers.containsKey(updatedUser.email)) {
        _mockUsers[updatedUser.email]!['user'] = updatedUser.toJson();
      }
      
      // Update in secure storage
      await _secureStorage.write(
        key: 'user_${updatedUser.uid}',
        value: jsonEncode(updatedUser.toJson()),
      );
      
      _currentUser = updatedUser;
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Profile update error: $e');
      return false;
    }
  }

  /// Private helper methods
  void _setAuthState(AuthState newState) {
    _authState = newState;
    notifyListeners();
  }

  Future<void> _updateLastLogin() async {
    if (_currentUser != null) {
      await _secureStorage.write(
        key: 'last_activity',
        value: DateTime.now().toIso8601String(),
      );
      await _secureStorage.write(
        key: 'current_user_id',
        value: _currentUser!.uid,
      );
    }
  }

  void _startSessionTimer() {
    _stopSessionTimer();
    _sessionTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      _checkSessionTimeout();
    });
  }

  void _stopSessionTimer() {
    _sessionTimer?.cancel();
    _sessionTimer = null;
  }

  Future<void> _checkSessionTimeout() async {
    try {
      final lastActivity = await _secureStorage.read(key: 'last_activity');
      if (lastActivity != null) {
        final lastActivityTime = DateTime.parse(lastActivity);
        final now = DateTime.now();
        
        if (now.difference(lastActivityTime) > _sessionTimeout) {
          await signOut();
        }
      }
    } catch (e) {
      debugPrint('Session timeout check error: $e');
    }
  }

  Future<bool> _isAccountLocked(String email) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final attempts = prefs.getInt('failed_attempts_$email') ?? 0;
      final lockTime = prefs.getString('lock_time_$email');
      
      if (attempts >= _maxLoginAttempts && lockTime != null) {
        final lockDateTime = DateTime.parse(lockTime);
        final now = DateTime.now();
        
        if (now.difference(lockDateTime) < _lockoutDuration) {
          return true;
        } else {
          // Lockout period expired, clear attempts
          await _clearFailedLoginAttempts(email);
        }
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  Future<void> _trackFailedLoginAttempt(String email) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final attempts = (prefs.getInt('failed_attempts_$email') ?? 0) + 1;
      
      await prefs.setInt('failed_attempts_$email', attempts);
      
      if (attempts >= _maxLoginAttempts) {
        await prefs.setString('lock_time_$email', DateTime.now().toIso8601String());
      }
    } catch (e) {
      debugPrint('Failed to track login attempt: $e');
    }
  }

  Future<void> _clearFailedLoginAttempts(String email) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('failed_attempts_$email');
      await prefs.remove('lock_time_$email');
    } catch (e) {
      debugPrint('Failed to clear login attempts: $e');
    }
  }

  @override
  void dispose() {
    _stopSessionTimer();
    super.dispose();
  }
}
